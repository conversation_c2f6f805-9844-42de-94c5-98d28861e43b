@import "tailwindcss";
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');

@theme {
  --color-border: #e5e7eb;
  --color-background: #ffffff;
  --color-foreground: #111827;
  --color-ring: #3b82f6;

  /* Define color palette */
  --color-primary-50: #eff6ff;
  --color-primary-100: #dbeafe;
  --color-primary-200: #bfdbfe;
  --color-primary-300: #93c5fd;
  --color-primary-400: #60a5fa;
  --color-primary-500: #3b82f6;
  --color-primary-600: #2563eb;
  --color-primary-700: #1d4ed8;
  --color-primary-800: #1e40af;
  --color-primary-900: #1e3a8a;

  --color-secondary-50: #f8fafc;
  --color-secondary-100: #f1f5f9;
  --color-secondary-200: #e2e8f0;
  --color-secondary-300: #cbd5e1;
  --color-secondary-400: #94a3b8;
  --color-secondary-500: #64748b;
  --color-secondary-600: #475569;
  --color-secondary-700: #334155;
  --color-secondary-800: #1e293b;
  --color-secondary-900: #0f172a;

  --color-success-50: #f0fdf4;
  --color-success-100: #dcfce7;
  --color-success-200: #bbf7d0;
  --color-success-300: #86efac;
  --color-success-400: #4ade80;
  --color-success-500: #22c55e;
  --color-success-600: #16a34a;
  --color-success-700: #15803d;
  --color-success-800: #166534;
  --color-success-900: #14532d;

  --color-warning-50: #fffbeb;
  --color-warning-100: #fef3c7;
  --color-warning-200: #fde68a;
  --color-warning-300: #fcd34d;
  --color-warning-400: #fbbf24;
  --color-warning-500: #f59e0b;
  --color-warning-600: #d97706;
  --color-warning-700: #b45309;
  --color-warning-800: #92400e;
  --color-warning-900: #78350f;
}

@layer base {
  * {
    border-color: var(--color-border);
  }

  html {
    height: 100%;
  }

  body {
    height: 100%;
    background-color: var(--color-background);
    color: var(--color-foreground);
    font-family: 'Inter', system-ui, sans-serif;
    font-feature-settings: "rlig" 1, "calt" 1;
    margin: 0;
    padding: 0;
  }

  #root {
    height: 100%;
    margin: 0;
    padding: 0;
  }

  .App {
    height: 100%;
  }
}

@layer components {
  /* POS specific components */

  .pos-button-primary {
    @apply inline-flex items-center justify-center rounded-lg px-4 py-2 text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-blue-500 focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 bg-blue-600 text-white hover:bg-blue-700;
  }

  .pos-button-secondary {
    @apply inline-flex items-center justify-center rounded-lg px-4 py-2 text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-blue-500 focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 bg-gray-200 text-gray-800 hover:bg-gray-300;
  }

  .pos-button-success {
    @apply inline-flex items-center justify-center rounded-lg px-4 py-2 text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-blue-500 focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 bg-green-500 text-white hover:bg-green-600;
  }

  .pos-button-warning {
    @apply inline-flex items-center justify-center rounded-lg px-4 py-2 text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-blue-500 focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 bg-yellow-500 text-white hover:bg-yellow-600;
  }

  .pos-button-error {
    @apply inline-flex items-center justify-center rounded-lg px-4 py-2 text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-blue-500 focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 bg-red-500 text-white hover:bg-red-600;
  }

  .pos-card {
    @apply rounded-lg border bg-white shadow-sm;
  }

  .pos-input {
    @apply flex h-10 w-full rounded-md border border-gray-300 bg-white px-3 py-2 text-sm placeholder:text-gray-500 focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-blue-500 focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50;
  }

  .menu-item-card {
    @apply rounded-lg border bg-white shadow-sm p-4 cursor-pointer transition-all hover:shadow-md hover:scale-105 active:scale-95;
  }

  .menu-item-card:hover {
    @apply shadow-lg;
  }

  .order-item {
    @apply flex items-center justify-between p-3 border-b border-gray-200 last:border-b-0;
  }

  .kitchen-order-card {
    @apply rounded-lg border bg-white shadow-sm p-4 m-2 min-w-80 max-w-sm;
  }

  .table-button {
    @apply rounded-lg border shadow-sm p-4 cursor-pointer transition-all hover:shadow-md text-center min-h-24 flex flex-col items-center justify-center;
  }

  .table-available {
    @apply rounded-lg border shadow-sm p-4 cursor-pointer transition-all hover:shadow-md text-center min-h-24 flex flex-col items-center justify-center bg-green-50 border-green-200 text-green-800 hover:bg-green-100;
  }

  .table-occupied {
    @apply rounded-lg border shadow-sm p-4 cursor-pointer transition-all hover:shadow-md text-center min-h-24 flex flex-col items-center justify-center bg-red-50 border-red-200 text-red-800 hover:bg-red-100;
  }

  .table-reserved {
    @apply rounded-lg border shadow-sm p-4 cursor-pointer transition-all hover:shadow-md text-center min-h-24 flex flex-col items-center justify-center bg-yellow-50 border-yellow-200 text-yellow-800 hover:bg-yellow-100;
  }

  /* Aspect ratio utilities for menu items */
  .aspect-w-16 {
    position: relative;
    padding-bottom: 56.25%; /* 16:9 aspect ratio */
  }

  .aspect-w-16 > * {
    position: absolute;
    height: 100%;
    width: 100%;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
  }

  /* Line clamp utility */
  .line-clamp-2 {
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }

  /* Custom scrollbar */
  .overflow-y-auto::-webkit-scrollbar {
    width: 6px;
  }

  .overflow-y-auto::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 3px;
  }

  .overflow-y-auto::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 3px;
  }

  .overflow-y-auto::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
  }
}
