/**
 * POS (Point of Sale) page for Restaurant POS system
 */

import React from 'react';
import { useAppSelector } from '../store';

const POSPage: React.FC = () => {
  const { user } = useAppSelector((state) => state.auth);
  const { items, total } = useAppSelector((state) => state.cart);

  return (
    <div className="h-screen flex bg-gray-50">
      {/* Menu Section */}
      <div className="flex-1 p-4">
        <div className="bg-white rounded-xl shadow-sm h-full flex flex-col">
          <div className="p-6 border-b border-gray-100">
            <div className="flex items-center justify-between">
              <div>
                <h1 className="text-3xl font-bold text-gray-900">Menu</h1>
                <p className="text-gray-600 mt-1">Select items to add to order</p>
              </div>
              <div className="text-sm text-gray-500">
                Welcome, {user?.first_name || 'User'}
              </div>
            </div>
          </div>

          <div className="flex-1 p-6 overflow-y-auto">
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
              {/* Sample menu items */}
              {[
                { id: 1, name: 'Margherita Pizza', desc: 'Fresh tomatoes, mozzarella, basil', price: 12.99, category: 'Pizza' },
                { id: 2, name: 'Caesar Salad', desc: 'Romaine lettuce, parmesan, croutons', price: 8.99, category: 'Salads' },
                { id: 3, name: 'Grilled Salmon', desc: 'Atlantic salmon with lemon butter', price: 18.99, category: 'Seafood' },
                { id: 4, name: 'Chicken Burger', desc: 'Grilled chicken, lettuce, tomato', price: 13.99, category: 'Burgers' },
                { id: 5, name: 'Pasta Carbonara', desc: 'Creamy pasta with bacon and eggs', price: 14.99, category: 'Pasta' },
                { id: 6, name: 'Chocolate Cake', desc: 'Rich chocolate cake with frosting', price: 6.99, category: 'Desserts' },
              ].map((item) => (
                <div key={item.id} className="menu-item-card group">
                  <div className="aspect-w-16 mb-4">
                    <div className="bg-gradient-to-br from-gray-100 to-gray-200 rounded-lg flex items-center justify-center">
                      <div className="text-center">
                        <div className="w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-2">
                          <span className="text-blue-600 font-bold text-lg">{item.name.charAt(0)}</span>
                        </div>
                        <span className="text-gray-500 text-sm">{item.category}</span>
                      </div>
                    </div>
                  </div>
                  <div className="space-y-2">
                    <h3 className="font-semibold text-gray-900 group-hover:text-blue-600 transition-colors">{item.name}</h3>
                    <p className="text-sm text-gray-600 line-clamp-2">{item.desc}</p>
                    <div className="flex items-center justify-between">
                      <p className="text-xl font-bold text-blue-600">${item.price}</p>
                      <button className="px-3 py-1 bg-blue-600 text-white text-sm rounded-lg hover:bg-blue-700 transition-colors">
                        Add
                      </button>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>
      </div>

      {/* Order Section */}
      <div className="w-96 bg-white shadow-xl rounded-l-xl flex flex-col">
        <div className="p-6 border-b border-gray-100">
          <div className="flex items-center justify-between">
            <div>
              <h2 className="text-xl font-bold text-gray-900">Current Order</h2>
              <p className="text-sm text-gray-600 flex items-center mt-1">
                <span className="w-2 h-2 bg-green-500 rounded-full mr-2"></span>
                Table #1 • Dine In
              </p>
            </div>
            <div className="text-right">
              <p className="text-sm text-gray-500">Order #001</p>
              <p className="text-xs text-gray-400">{new Date().toLocaleTimeString()}</p>
            </div>
          </div>
        </div>

        <div className="flex-1 p-6 overflow-y-auto">
          {items.length === 0 ? (
            <div className="text-center text-gray-500 mt-12">
              <div className="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <svg className="w-8 h-8 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 11V7a4 4 0 00-8 0v4M5 9h14l1 12H4L5 9z" />
                </svg>
              </div>
              <p className="font-medium">No items in order</p>
              <p className="text-sm mt-1">Select items from the menu to get started</p>
            </div>
          ) : (
            <div className="space-y-3">
              {items.map((item) => (
                <div key={item.id} className="order-item bg-gray-50 rounded-lg">
                  <div className="flex-1">
                    <h4 className="font-medium text-gray-900">{item.name}</h4>
                    <p className="text-sm text-gray-600">Qty: {item.quantity}</p>
                    {item.special_instructions && (
                      <p className="text-xs text-orange-600 mt-1">Note: {item.special_instructions}</p>
                    )}
                  </div>
                  <div className="text-right">
                    <p className="font-semibold text-gray-900">${item.subtotal.toFixed(2)}</p>
                    <button className="text-xs text-red-500 hover:text-red-700 mt-1">Remove</button>
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>

        {/* Order Summary */}
        <div className="border-t border-gray-100 p-6 bg-gray-50 rounded-bl-xl">
          <div className="space-y-3">
            <div className="flex justify-between text-sm">
              <span className="text-gray-600">Subtotal</span>
              <span className="font-medium">${total.toFixed(2)}</span>
            </div>
            <div className="flex justify-between text-sm">
              <span className="text-gray-600">Tax (8.75%)</span>
              <span className="font-medium">${(total * 0.0875).toFixed(2)}</span>
            </div>
            <div className="flex justify-between text-sm">
              <span className="text-gray-600">Service Charge</span>
              <span className="font-medium">$0.00</span>
            </div>
            <div className="border-t border-gray-200 pt-3">
              <div className="flex justify-between text-lg font-bold">
                <span>Total</span>
                <span className="text-blue-600">${(total * 1.0875).toFixed(2)}</span>
              </div>
            </div>
          </div>

          <div className="mt-6 space-y-3">
            <button className="pos-button-success w-full py-3 text-base font-semibold">
              💳 Process Payment
            </button>
            <div className="grid grid-cols-2 gap-3">
              <button className="pos-button-secondary py-2 text-sm">
                📋 Hold Order
              </button>
              <button className="pos-button-warning py-2 text-sm">
                🗑️ Clear All
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default POSPage;
